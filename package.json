{"name": "image-gen-mcp", "version": "1.0.0", "description": "MCP Server for image generation using Replicate's flux-schnell model", "type": "module", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "node --loader ts-node/esm src/server.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mcp", "image-generation", "replicate", "flux-schnell"], "author": "", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "^1.4.1", "@types/node": "^20.0.0", "dotenv": "^16.0.0", "replicate": "^0.25.0", "typescript": "^5.0.0", "zod": "^3.24.1"}, "devDependencies": {"ts-node": "^10.9.0"}}