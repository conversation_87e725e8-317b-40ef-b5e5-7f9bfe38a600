const https = require('https');
const fs = require('fs');
const path = require('path');

// Load environment variables manually
require('dotenv').config();

const API_TOKEN = process.env.REPLICATE_API_TOKEN;

console.log('API Token:', API_TOKEN ? 'Found' : 'Not found');

// Test API connection
const testAPI = () => {
  const options = {
    hostname: 'api.replicate.com',
    port: 443,
    path: '/v1/account',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${API_TOKEN}`,
      'Content-Type': 'application/json'
    }
  };

  const req = https.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('Response:', data);
      if (res.statusCode === 200) {
        console.log('API Token is valid!');
        generateImage();
      } else {
        console.log('API Token validation failed');
      }
    });
  });

  req.on('error', (e) => {
    console.error(`Problem with request: ${e.message}`);
  });

  req.end();
};

// Generate image function
const generateImage = () => {
  const postData = JSON.stringify({
    input: {
      prompt: "A beautiful woman with elegant features, soft lighting, photorealistic portrait",
      go_fast: true,
      megapixels: "1",
      num_outputs: 1,
      aspect_ratio: "1:1",
      num_inference_steps: 4
    }
  });

  const options = {
    hostname: 'api.replicate.com',
    port: 443,
    path: '/v1/models/black-forest-labs/flux-schnell/predictions',
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${API_TOKEN}`,
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = https.request(options, (res) => {
    console.log(`Image generation status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('Image generation response:', data);
      
      if (res.statusCode === 201) {
        const response = JSON.parse(data);
        console.log('Image generation started successfully!');
        console.log('Prediction ID:', response.id);
        
        // Poll for completion
        if (response.id) {
          pollForCompletion(response.id);
        }
      }
    });
  });

  req.on('error', (e) => {
    console.error(`Problem with image generation request: ${e.message}`);
  });

  req.write(postData);
  req.end();
};

// Poll for completion
const pollForCompletion = (predictionId) => {
  const checkStatus = () => {
    const options = {
      hostname: 'api.replicate.com',
      port: 443,
      path: `/v1/predictions/${predictionId}`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`,
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const response = JSON.parse(data);
        console.log(`Status: ${response.status}`);
        
        if (response.status === 'succeeded') {
          console.log('Image generated successfully!');
          console.log('Image URLs:', response.output);
          
          // Download the first image
          if (response.output && response.output.length > 0) {
            downloadImage(response.output[0], 'beautiful_woman.png');
          }
        } else if (response.status === 'failed') {
          console.log('Image generation failed:', response.error);
        } else {
          // Still processing, check again in 2 seconds
          setTimeout(checkStatus, 2000);
        }
      });
    });

    req.on('error', (e) => {
      console.error(`Problem checking status: ${e.message}`);
    });

    req.end();
  };

  checkStatus();
};

// Download image
const downloadImage = (url, filename) => {
  https.get(url, (res) => {
    const filePath = path.join(__dirname, filename);
    const fileStream = fs.createWriteStream(filePath);
    
    res.pipe(fileStream);
    
    fileStream.on('finish', () => {
      fileStream.close();
      console.log(`Image saved as: ${filePath}`);
    });
  }).on('error', (e) => {
    console.error(`Problem downloading image: ${e.message}`);
  });
};

// Start the test
testAPI();
